import { IPriceLine } from 'lightweight-charts';
import {
  DragLineType,
  LineMetadata,
  DragState,
  DragToleranceConfig,
  OrderExecutionType
} from '@/types/trading';

// Default configuration for drag tolerances
export const DEFAULT_DRAG_TOLERANCE: DragToleranceConfig = {
  lineProximity: 8, // 8 pixels tolerance for line detection
  minDragDistance: 3, // 3 pixels minimum to start drag
  priceSnapTolerance: 0.00001, // Very small price tolerance for snapping
};

/**
 * Calculate the distance between a point and a horizontal line
 * @param pointY - Y coordinate of the point
 * @param linePrice - Price of the line
 * @param priceToCoordinate - Function to convert price to Y coordinate
 * @returns Distance in pixels
 */
export function calculateLineDistance(
  pointY: number,
  linePrice: number,
  priceToCoordinate: (price: number) => number | null
): number {
  const lineY = priceToCoordinate(linePrice);
  if (lineY === null) return Infinity;
  return Math.abs(pointY - lineY);
}

/**
 * Find the closest draggable line to a given point
 * @param pointY - Y coordinate of the point
 * @param lineMetadataMap - Map of line metadata
 * @param priceToCoordinate - Function to convert price to Y coordinate
 * @param tolerance - Maximum distance to consider a line "close"
 * @returns Closest line metadata or null if none found within tolerance
 */
export function findClosestLine(
  pointY: number,
  lineMetadataMap: Map<IPriceLine, LineMetadata>,
  priceToCoordinate: (price: number) => number | null,
  tolerance: number = DEFAULT_DRAG_TOLERANCE.lineProximity
): { line: IPriceLine; metadata: LineMetadata; distance: number } | null {
  let closestLine: IPriceLine | null = null;
  let closestMetadata: LineMetadata | null = null;
  let minDistance = Infinity;

  for (const [line, metadata] of lineMetadataMap.entries()) {
    const distance = calculateLineDistance(pointY, metadata.originalPrice, priceToCoordinate);
    
    if (distance < tolerance && distance < minDistance) {
      closestLine = line;
      closestMetadata = metadata;
      minDistance = distance;
    }
  }

  if (closestLine && closestMetadata) {
    return { line: closestLine, metadata: closestMetadata, distance: minDistance };
  }

  return null;
}

/**
 * Determine if a line type is draggable
 * @param lineType - Type of the line
 * @returns True if the line can be dragged
 */
export function isLineDraggable(lineType: DragLineType): boolean {
  switch (lineType) {
    case DragLineType.PENDING_ORDER:
    case DragLineType.STOP_LOSS:
    case DragLineType.TAKE_PROFIT:
      return true;
    case DragLineType.POSITION_ENTRY:
      return true; // Can be dragged to create SL/TP, but not to change entry price
    default:
      return false;
  }
}

/**
 * Calculate drag direction based on start and current Y coordinates
 * @param startY - Starting Y coordinate
 * @param currentY - Current Y coordinate
 * @param minDistance - Minimum distance to determine direction
 * @returns Direction of drag or null if too small
 */
export function calculateDragDirection(
  startY: number,
  currentY: number,
  minDistance: number = DEFAULT_DRAG_TOLERANCE.minDragDistance
): 'up' | 'down' | null {
  const deltaY = currentY - startY;
  
  if (Math.abs(deltaY) < minDistance) {
    return null;
  }
  
  return deltaY < 0 ? 'up' : 'down';
}

/**
 * Validate if a pending order price is valid based on order type and current market
 * @param orderType - Type of the order execution
 * @param newPrice - New price to validate
 * @param currentBid - Current bid price
 * @param currentAsk - Current ask price
 * @param minDistance - Minimum distance from current price (in price units)
 * @returns Object with validation result and error message
 */
export function validatePendingOrderPrice(
  orderType: OrderExecutionType,
  newPrice: number,
  currentBid: number,
  currentAsk: number,
  minDistance: number = 0.0001
): { isValid: boolean; errorMessage?: string } {
  // currentPrice removed as it was unused
  
  switch (orderType) {
    case 'buyLimit':
      const maxBuyLimit = currentAsk - minDistance;
      if (newPrice >= maxBuyLimit) {
        return {
          isValid: false,
          errorMessage: `Buy limit must be below ${maxBuyLimit.toFixed(5)} (at least 1 pip below ask price)`
        };
      }
      break;
      
    case 'sellLimit':
      const minSellLimit = currentBid + minDistance;
      if (newPrice <= minSellLimit) {
        return {
          isValid: false,
          errorMessage: `Sell limit must be above ${minSellLimit.toFixed(5)} (at least 1 pip above bid price)`
        };
      }
      break;
      
    case 'buyStop':
      const minBuyStop = currentAsk + minDistance;
      if (newPrice <= minBuyStop) {
        return {
          isValid: false,
          errorMessage: `Buy stop must be above ${minBuyStop.toFixed(5)} (at least 1 pip above ask price)`
        };
      }
      break;
      
    case 'sellStop':
      const maxSellStop = currentBid - minDistance;
      if (newPrice >= maxSellStop) {
        return {
          isValid: false,
          errorMessage: `Sell stop must be below ${maxSellStop.toFixed(5)} (at least 1 pip below bid price)`
        };
      }
      break;
      
    default:
      return { isValid: true };
  }
  
  return { isValid: true };
}

/**
 * Validate stop loss and take profit levels for a position
 * @param positionType - 'buy' or 'sell'
 * @param entryPrice - Entry price of the position
 * @param stopLoss - Stop loss price (can be null)
 * @param takeProfit - Take profit price (can be null)
 * @param minDistance - Minimum distance from entry price
 * @returns Object with validation result and error message
 */
export function validateStopLossTakeProfit(
  positionType: 'buy' | 'sell',
  entryPrice: number,
  stopLoss: number | null,
  takeProfit: number | null,
  minDistance: number = 0.0001
): { isValid: boolean; errorMessage?: string } {
  if (stopLoss !== null) {
    if (positionType === 'buy') {
      if (stopLoss >= entryPrice) {
        return { isValid: false, errorMessage: 'Stop loss for buy position must be below entry price' };
      }
      if (entryPrice - stopLoss < minDistance) {
        return { isValid: false, errorMessage: `Stop loss must be at least ${minDistance} below entry` };
      }
    } else {
      if (stopLoss <= entryPrice) {
        return { isValid: false, errorMessage: 'Stop loss for sell position must be above entry price' };
      }
      if (stopLoss - entryPrice < minDistance) {
        return { isValid: false, errorMessage: `Stop loss must be at least ${minDistance} above entry` };
      }
    }
  }
  
  if (takeProfit !== null) {
    if (positionType === 'buy') {
      if (takeProfit <= entryPrice) {
        return { isValid: false, errorMessage: 'Take profit for buy position must be above entry price' };
      }
      if (takeProfit - entryPrice < minDistance) {
        return { isValid: false, errorMessage: `Take profit must be at least ${minDistance} above entry` };
      }
    } else {
      if (takeProfit >= entryPrice) {
        return { isValid: false, errorMessage: 'Take profit for sell position must be below entry price' };
      }
      if (entryPrice - takeProfit < minDistance) {
        return { isValid: false, errorMessage: `Take profit must be at least ${minDistance} below entry` };
      }
    }
  }
  
  return { isValid: true };
}

/**
 * Create line metadata for different types of lines
 */
export function createLineMetadata(
  id: string,
  type: DragLineType,
  originalPrice: number,
  orderId?: string,
  positionId?: string
): LineMetadata {
  return {
    id,
    type,
    originalPrice,
    orderId,
    positionId,
    isValid: true
  };
}

/**
 * Create initial drag state
 */
export function createInitialDragState(): DragState {
  return {
    isDragging: false,
    draggedLineId: null,
    draggedLineType: null,
    startPrice: 0,
    currentPrice: 0,
    startY: 0,
    currentY: 0,
    isValidPosition: true,
    dragDirection: null
  };
}

/**
 * Debug utility to inspect line metadata
 * @param lineMetadataMap - Map of line metadata
 * @returns Debug information about all lines
 */
export function debugLineMetadata(lineMetadataMap: Map<IPriceLine, LineMetadata>): {
  totalLines: number;
  linesByType: Record<string, number>;
  lines: Array<{ id: string; type: DragLineType; price: number; isValid: boolean }>;
} {
  const linesByType: Record<string, number> = {};
  const lines: Array<{ id: string; type: DragLineType; price: number; isValid: boolean }> = [];

  for (const [, metadata] of lineMetadataMap.entries()) {
    // Count by type
    linesByType[metadata.type] = (linesByType[metadata.type] || 0) + 1;

    // Add to lines array
    lines.push({
      id: metadata.id,
      type: metadata.type,
      price: metadata.originalPrice,
      isValid: metadata.isValid
    });
  }

  return {
    totalLines: lineMetadataMap.size,
    linesByType,
    lines
  };
}

/**
 * Get line metadata by ID
 * @param lineMetadataMap - Map of line metadata
 * @param lineId - ID of the line to find
 * @returns Line metadata or null if not found
 */
export function getLineMetadataById(
  lineMetadataMap: Map<IPriceLine, LineMetadata>,
  lineId: string
): { line: IPriceLine; metadata: LineMetadata } | null {
  for (const [line, metadata] of lineMetadataMap.entries()) {
    if (metadata.id === lineId) {
      return { line, metadata };
    }
  }
  return null;
}

/**
 * Validate stop loss and take profit levels for drag operations
 * @param lineType - Type of line (STOP_LOSS or TAKE_PROFIT)
 * @param positionType - Type of position (buy or sell)
 * @param price - Proposed price
 * @param entryPrice - Position entry price
 * @param minDistance - Minimum distance from entry price
 * @returns Validation result
 */
export function validateDragStopLossTakeProfit(
  lineType: DragLineType,
  positionType: 'buy' | 'sell',
  price: number,
  entryPrice: number,
  minDistance: number
): ValidationResult {
  if (lineType === DragLineType.STOP_LOSS) {
    if (positionType === 'buy') {
      // Stop loss for buy position must be below entry price
      if (price >= entryPrice - minDistance) {
        const maxStopLoss = entryPrice - minDistance;
        return {
          isValid: false,
          errorMessage: `Stop loss must be below ${maxStopLoss.toFixed(5)} (at least 1 pip below entry)`
        };
      }
    } else {
      // Stop loss for sell position must be above entry price
      if (price <= entryPrice + minDistance) {
        const minStopLoss = entryPrice + minDistance;
        return {
          isValid: false,
          errorMessage: `Stop loss must be above ${minStopLoss.toFixed(5)} (at least 1 pip above entry)`
        };
      }
    }
  } else if (lineType === DragLineType.TAKE_PROFIT) {
    if (positionType === 'buy') {
      // Take profit for buy position must be above entry price
      if (price <= entryPrice + minDistance) {
        const minTakeProfit = entryPrice + minDistance;
        return {
          isValid: false,
          errorMessage: `Take profit must be above ${minTakeProfit.toFixed(5)} (at least 1 pip above entry)`
        };
      }
    } else {
      // Take profit for sell position must be below entry price
      if (price >= entryPrice - minDistance) {
        const maxTakeProfit = entryPrice - minDistance;
        return {
          isValid: false,
          errorMessage: `Take profit must be below ${maxTakeProfit.toFixed(5)} (at least 1 pip below entry)`
        };
      }
    }
  }

  return { isValid: true };
}
